import { NextRequest, NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';

interface RelatorioData {
  id: string;
  titulo: string;
  descricao: string;
  tipo: 'performance' | 'processos' | 'compliance' | 'financeiro' | 'operacional';
  categoria: 'estrategico' | 'gerencial' | 'operacional';
  status: 'atualizado' | 'pendente' | 'processando';
  visualizacoes: number;
  downloads: number;
  dataGeracao: string;
  dados: any;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tipo = searchParams.get('tipo');
    const categoria = searchParams.get('categoria');
    const relatorioId = searchParams.get('id');

    // Se for solicitação de relatório específico
    if (relatorioId) {
      return await gerarRelatorioEspecifico(relatorioId);
    }

    // Carregar dados dos processos
    const csvReader = new CSVReader();
    const processos = await csvReader.lerProcessos();

    // Gerar relatórios disponíveis
    const relatorios = await gerarListaRelatorios(processos);

    // Filtrar se necessário
    let relatoriosFiltrados = relatorios;
    if (tipo) {
      relatoriosFiltrados = relatoriosFiltrados.filter(r => r.tipo === tipo);
    }
    if (categoria) {
      relatoriosFiltrados = relatoriosFiltrados.filter(r => r.categoria === categoria);
    }

    // Estatísticas
    const stats = {
      total: relatorios.length,
      atualizados: relatorios.filter(r => r.status === 'atualizado').length,
      pendentes: relatorios.filter(r => r.status === 'pendente').length,
      processando: relatorios.filter(r => r.status === 'processando').length,
      totalVisualizacoes: relatorios.reduce((sum, r) => sum + r.visualizacoes, 0),
      totalDownloads: relatorios.reduce((sum, r) => sum + r.downloads, 0)
    };

    return NextResponse.json({
      success: true,
      data: {
        relatorios: relatoriosFiltrados,
        stats
      }
    });
  } catch (error) {
    console.error('Erro na API de relatórios:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

async function gerarListaRelatorios(processos: any[]): Promise<RelatorioData[]> {
  const agora = new Date().toISOString();
  
  return [
    {
      id: 'dashboard-executivo',
      titulo: 'Dashboard Executivo',
      descricao: 'Visão geral de todos os processos, contratos e métricas de performance',
      tipo: 'performance',
      categoria: 'estrategico',
      status: 'atualizado',
      visualizacoes: 245,
      downloads: 89,
      dataGeracao: agora,
      dados: await gerarDadosDashboardExecutivo(processos)
    },
    {
      id: 'processos-secretaria',
      titulo: 'Relatório de Processos por Secretaria',
      descricao: 'Análise detalhada do desempenho de cada secretaria',
      tipo: 'processos',
      categoria: 'gerencial',
      status: 'atualizado',
      visualizacoes: 156,
      downloads: 67,
      dataGeracao: agora,
      dados: await gerarDadosProcessosSecretaria(processos)
    },
    {
      id: 'tempo-processamento',
      titulo: 'Análise de Tempo de Processamento',
      descricao: 'Métricas de eficiência e gargalos no fluxo',
      tipo: 'performance',
      categoria: 'operacional',
      status: 'atualizado',
      visualizacoes: 98,
      downloads: 45,
      dataGeracao: agora,
      dados: await gerarDadosTempoProcessamento(processos)
    },
    {
      id: 'compliance',
      titulo: 'Relatório de Compliance',
      descricao: 'Conformidade com Lei 14.133/21 e Decreto Municipal',
      tipo: 'compliance',
      categoria: 'estrategico',
      status: 'atualizado',
      visualizacoes: 134,
      downloads: 78,
      dataGeracao: agora,
      dados: await gerarDadosCompliance(processos)
    },
    {
      id: 'financeiro-contratos',
      titulo: 'Análise Financeira de Contratos',
      descricao: 'Economia gerada e performance de fornecedores',
      tipo: 'financeiro',
      categoria: 'gerencial',
      status: 'atualizado',
      visualizacoes: 89,
      downloads: 34,
      dataGeracao: agora,
      dados: await gerarDadosFinanceiros(processos)
    },
    {
      id: 'usuarios-acessos',
      titulo: 'Relatório de Usuários e Acessos',
      descricao: 'Atividade dos usuários e logs de segurança',
      tipo: 'operacional',
      categoria: 'operacional',
      status: 'atualizado',
      visualizacoes: 67,
      downloads: 23,
      dataGeracao: agora,
      dados: await gerarDadosUsuarios()
    }
  ];
}

async function gerarRelatorioEspecifico(relatorioId: string) {
  const csvReader = new CSVReader();
  const processos = await csvReader.lerProcessos();

  switch (relatorioId) {
    case 'dashboard-executivo':
      return NextResponse.json({
        success: true,
        data: await gerarDadosDashboardExecutivo(processos)
      });
    
    case 'processos-secretaria':
      return NextResponse.json({
        success: true,
        data: await gerarDadosProcessosSecretaria(processos)
      });
    
    case 'tempo-processamento':
      return NextResponse.json({
        success: true,
        data: await gerarDadosTempoProcessamento(processos)
      });
    
    case 'compliance':
      return NextResponse.json({
        success: true,
        data: await gerarDadosCompliance(processos)
      });
    
    case 'financeiro-contratos':
      return NextResponse.json({
        success: true,
        data: await gerarDadosFinanceiros(processos)
      });
    
    case 'usuarios-acessos':
      return NextResponse.json({
        success: true,
        data: await gerarDadosUsuarios()
      });
    
    default:
      return NextResponse.json(
        { success: false, error: 'Relatório não encontrado' },
        { status: 404 }
      );
  }
}

async function gerarDadosDashboardExecutivo(processos: any[]) {
  const total = processos.length;
  const finalizados = processos.filter(p => 
    p.STATUS?.includes('Finalizado') || 
    p.STATUS?.includes('encaminhado à secretaria')
  ).length;
  
  const emAndamento = total - finalizados;
  const eficiencia = total > 0 ? ((finalizados / total) * 100).toFixed(1) : '0';

  return {
    resumo: {
      totalProcessos: total,
      processosFinalizados: finalizados,
      processosEmAndamento: emAndamento,
      eficienciaGeral: `${eficiencia}%`
    },
    distribuicaoModalidade: processos.reduce((acc, p) => {
      const modalidade = p.MODALIDADE || 'Não informado';
      acc[modalidade] = (acc[modalidade] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    distribuicaoSecretaria: processos.reduce((acc, p) => {
      const secretaria = p.REQUISITANTE || 'Não informado';
      acc[secretaria] = (acc[secretaria] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    tendenciaMensal: gerarTendenciaMensal(processos)
  };
}

async function gerarDadosProcessosSecretaria(processos: any[]) {
  const secretarias = processos.reduce((acc, p) => {
    const secretaria = p.REQUISITANTE || 'Não informado';
    if (!acc[secretaria]) {
      acc[secretaria] = {
        nome: secretaria,
        total: 0,
        finalizados: 0,
        emAndamento: 0,
        tempoMedio: 0,
        eficiencia: 0
      };
    }
    
    acc[secretaria].total++;
    
    if (p.STATUS?.includes('Finalizado') || p.STATUS?.includes('encaminhado à secretaria')) {
      acc[secretaria].finalizados++;
    } else {
      acc[secretaria].emAndamento++;
    }
    
    return acc;
  }, {} as Record<string, any>);

  // Calcular eficiência
  Object.values(secretarias).forEach((sec: any) => {
    sec.eficiencia = sec.total > 0 ? ((sec.finalizados / sec.total) * 100).toFixed(1) : '0';
    sec.tempoMedio = Math.floor(Math.random() * 30) + 15; // Mock tempo médio
  });

  return {
    secretarias: Object.values(secretarias),
    ranking: Object.values(secretarias)
      .sort((a: any, b: any) => parseFloat(b.eficiencia) - parseFloat(a.eficiencia))
      .slice(0, 5)
  };
}

async function gerarDadosTempoProcessamento(processos: any[]) {
  return {
    tempoMedioCLMP: 18.5,
    tempoMedioGeral: 45.2,
    gargalos: [
      { etapa: 'Análise Orçamentária (SF)', tempoMedio: 12.3, processos: 23 },
      { etapa: 'Parecer Jurídico (SAJ)', tempoMedio: 8.7, processos: 18 },
      { etapa: 'Adequações', tempoMedio: 6.2, processos: 28 },
      { etapa: 'Publicação', tempoMedio: 3.1, processos: 45 }
    ],
    distribuicaoTempo: {
      'Até 15 dias': 45,
      '16-30 dias': 38,
      '31-60 dias': 12,
      'Mais de 60 dias': 5
    }
  };
}

async function gerarDadosCompliance(processos: any[]) {
  return {
    scoreGeral: 94.2,
    conformidadeLei14133: 96.8,
    conformidadeDecreto: 91.5,
    violacoes: [
      { tipo: 'ETP Incompleta', quantidade: 3, gravidade: 'MEDIA' },
      { tipo: 'Prazo Inadequado', quantidade: 1, gravidade: 'BAIXA' },
      { tipo: 'Documentação Faltante', quantidade: 2, gravidade: 'ALTA' }
    ],
    recomendacoes: [
      'Revisar templates de ETP',
      'Implementar checklist automático',
      'Treinamento em Lei 14.133/21'
    ]
  };
}

async function gerarDadosFinanceiros(processos: any[]) {
  const valorTotal = processos.reduce((sum, p) => {
    const valor = p['VALOR ESTIMADO']?.replace(/[R$\s.,]/g, '') || '0';
    return sum + parseFloat(valor) || 0;
  }, 0);

  return {
    valorTotalProcessos: valorTotal,
    economiaGerada: valorTotal * 0.12, // 12% economia estimada
    contratos: {
      vigentes: 15,
      valorVigentes: valorTotal * 0.65,
      vencendoEm30Dias: 3
    },
    fornecedores: {
      total: 89,
      novos: 12,
      performance: 87.3
    }
  };
}

async function gerarDadosUsuarios() {
  return {
    usuariosAtivos: 24,
    acessosHoje: 156,
    tempoMedioSessao: '2h 15m',
    modulosMaisUsados: [
      { modulo: 'Processos', acessos: 89 },
      { modulo: 'Dashboard', acessos: 67 },
      { modulo: 'Pesquisa Preços', acessos: 45 },
      { modulo: 'Análise Editais', acessos: 23 }
    ]
  };
}

function gerarTendenciaMensal(processos: any[]) {
  // Mock de tendência mensal
  return [
    { mes: 'Jan', processos: 18 },
    { mes: 'Fev', processos: 22 },
    { mes: 'Mar', processos: 28 },
    { mes: 'Abr', processos: 35 },
    { mes: 'Mai', processos: 31 },
    { mes: 'Jun', processos: 19 }
  ];
}
